import path from 'path'
import { pluginRoot } from './model/path.js'

// 支持锅巴
export function supportGuoba() {
  return {
    // 插件信息，将会显示在前端页面
    pluginInfo: {
      name: 'video-parser-plugin',
      title: '视频解析插件',
      author: '@Misaka20002',
      authorLink: 'https://github.com/misaka20002',
      link: 'https://github.com/your-repo/video-parser-plugin',
      isV3: true,
      isV2: false,
      description: '抖音/B站视频解析插件',
      // 显示图标，此为个性化配置
      // 图标可在 https://icon-sets.iconify.design 这里进行搜索
      icon: 'mdi:video',
      // 图标颜色，例：#FF0000 或 rgb(255, 0, 0)
      iconColor: '#d19fe8',
      // 如果想要显示成图片，也可以填写图标路径（绝对路径）
      iconPath: path.join(pluginRoot, 'resources/help/icon.png'),
    },
    // 配置项信息
    configInfo: {
      // 配置项 schemas
      schemas: [
        {
          component: 'Divider',
          label: '抖音解析配置',
          componentProps: {
            orientation: 'left',
            plain: true,
          },
        },
        {
          field: 'douyinTV',
          label: '开启抖音解析',
          bottomHelpMessage: '是否开启抖音视频/图集解析功能',
          component: 'Switch',
        },
        {
          field: 'douyin_maxSizeMB',
          label: '抖音视频最大大小(MB)',
          bottomHelpMessage: '抖音视频文件大小限制，超过此大小将不会下载',
          component: 'InputNumber',
          componentProps: {
            placeholder: '请输入最大文件大小',
            min: 1,
            max: 500,
          },
        },
        {
          component: 'Divider',
          label: 'B站解析配置',
          componentProps: {
            orientation: 'left',
            plain: true,
          },
        },
        {
          field: 'turnOnBilitv',
          label: '开启B站解析',
          bottomHelpMessage: '是否开启B站视频解析功能',
          component: 'Switch',
        },
        {
          field: 'bilitv_max_duration_min',
          label: 'B站视频最大时长(分钟)',
          bottomHelpMessage: 'B站视频时长限制，超过此时长将不会下载',
          component: 'InputNumber',
          componentProps: {
            placeholder: '请输入最大时长',
            min: 1,
            max: 60,
          },
        },
      ],
      // 获取配置
      getConfigData() {
        const Config = (await import('./components/Config.js')).default
        return Config.getConfig()
      },
      // 设置配置
      setConfigData(data, { Result }) {
        const Config = (await import('./components/Config.js')).default
        const result = Config.setConfig(data)
        if (result) {
          return Result.ok({}, '保存成功~')
        } else {
          return Result.error('保存失败！')
        }
      },
    },
  }
}
