* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  user-select: none;
}
body {
  font-size: 18px;
  color: #1e1f20;
  transform: scale(1.3);
  transform-origin: 0 0;
  width: 600px;
}
.container {
  width: 600px;
  padding: 10px 0 10px 0;
  background-size: 100% 100%;
}
.log-cont {
  background-size: cover;
  margin: 5px 15px 5px 10px;
  border-radius: 10px;
}
.log-cont .cont {
  margin: 0;
}
.log-cont .cont-title {
  font-size: 16px;
  padding: 10px 20px 6px;
}
.log-cont .cont-title.current-version {
  font-size: 20px;
}
.log-cont ul {
  font-size: 14px;
  padding-left: 20px;
}
.log-cont ul li {
  margin: 3px 0;
}
.log-cont ul.sub-log-ul li {
  margin: 1px 0;
}
.log-cont .cmd {
  color: #d3bc8e;
  display: inline-block;
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.5);
  padding: 0 3px;
  margin: 1px 2px;
}
.log-cont .strong {
  color: #24d5cd;
}
.log-cont .new {
  display: inline-block;
  width: 18px;
  margin: 0 -3px 0 1px;
}
.log-cont .new:before {
  content: "NEW";
  display: inline-block;
  transform: scale(0.6);
  transform-origin: 0 0;
  color: #d3bc8e;
  white-space: nowrap;
}
.dev-cont {
  background: none;
}
.dev-cont .cont-title {
  background: rgba(0, 0, 0, 0.7);
}
.dev-cont .cont-body {
  background: rgba(0, 0, 0, 0.5);
}
.dev-cont .cont-body.dev-info {
  background: rgba(0, 0, 0, 0.2);
}
.dev-cont .strong {
  font-size: 15px;
}
/*# sourceMappingURL=version-info.css.map */