import plugin from '../../../lib/plugins/plugin.js'

export class help extends plugin {
    constructor() {
        super({
            /** 功能名称 */
            name: '视频解析插件帮助',
            /** 功能描述 */
            dsc: '视频解析插件帮助',
            event: 'message',
            /** 优先级，数字越小等级越高 */
            priority: 1009,
            rule: [
                {
                    /** 命令正则匹配 */
                    reg: '^(/|#)(视频解析|解析)帮助$',
                    /** 执行方法 */
                    fnc: 'help'
                }
            ]
        })
    }

    async help(e) {
        const helpText = `
视频解析插件帮助

功能说明：
1. 抖音链接解析
   - 发送抖音链接自动解析视频/图集
   - 支持短链接和长链接
   - 自动下载视频和图片

2. B站视频解析
   - 发送B站视频链接自动解析视频
   - 支持BV号和av号
   - 自动下载视频

使用方法：
直接发送抖音或B站链接即可自动解析

配置说明：
- 抖音解析：默认开启，最大视频大小50MB
- B站解析：默认开启，最大视频时长10分钟
        `.trim()

        await e.reply(helpText, true)
        return true
    }


}

