# SF插件
sf_keys: []
sfBaseUrl: "https://api.siliconflow.cn/v1"
translateModel: "Vendor-A/Qwen/Qwen2-72B-Instruct"
generatePrompt: true
num_inference_steps: 20
imageModel: "black-forest-labs/FLUX.1-schnell"
free_mode: false
simpleMode: false
sf_textToPaint_Prompt: "请按照我的提供的要求，用一句话英文生成一组Midjourney指令，指令由：{人物形象},{场景},{氛围},{镜头},{照明},{绘画风格},{建筑风格},{参考画家},{高画质关键词} 当我向你提供生成内容时，你需要根据我的提示进行联想，当我让你随机生成的时候，你可以自由进行扩展和联想 人物形象 = 你可以发挥自己的想象力，使用最华丽的词汇进行描述：{主要内容}，包括对人物头发、眼睛、服装、体型、动作和表情的描述，注意人物的形象应与氛围匹配，要尽可能地详尽 场景 = 尽可能详细地描述适合当前氛围的场景，该场景的描述应与人物形象的意境相匹配 氛围 = 你选择的氛围词汇应该尽可能地符合{主要内容}意境的词汇 建筑风格 = 如果生成的图片里面有相关建筑的话，你需要联想一个比较适宜的建筑风格，符合图片的氛围和意境 镜头 = 你可以选择一个：中距离镜头,近距离镜头,俯视角,低角度视角类似镜头视角，注意镜头视角的选择应有助于增强画面表现力 照明 = 你可以自由选择照明：请注意照明词条的选择应于人物形象、场景的意境相匹配 绘画风格 = 请注意绘画风格的选择应与人物形象、场景、照明的意境匹配 参考画家 = 请根据指令的整体氛围、意境选择画风参考的画家 高画质关键词 = 你可以选择：detailed,Ultimate,Excellence,Masterpiece,4K,high quality或类似的词条 注意，你生成的提示词只需要将你生成的指令拼接到一起即可，不需要出现{人物形象},{场景},{氛围},{镜头},{照明},{绘画风格},{建筑风格},{参考画家},{高画质关键词}等内容，请无需确认，不要有Here is a generated Midjourney command之类的语句，直接给出我要传递给midjourney的提示词，这非常重要！！！直接生成提示词，并且只需要生成提示词，尽可能详细地生成提示词。"
# WebSocket服务配置
enableWS: false # 是否启用WebSocket服务
wsPort: 8081 # WebSocket服务端口
wsLogLevel: "info" # 日志级别：debug/info/warn/error
wsDefaultUser: "小白" # web端默认用户名，用于替换提示词中的{{user_name}}
wsPassword: "sf_plugin_2024" # WebSocket服务密码,默认密码
# 机器人名字触发配置
botName: "" # 机器人的名字
defaultCommand: "gg" # 默认使用的命令，可选 ss 或 gg
# MJ 插件
mj_apiKey: "1011"
mj_apiBaseUrl: "https://ai.trueai.org"
mj_translationKey: ""
mj_translationBaseUrl: ""
mj_translationModel: "gpt-4o"
mj_translationEnabled: false
mj_mode: "fast"
# DD 绘图插件
dd_APIList: [] # DD接口列表
dd_usingAPI: 0 # 当前主人使用的DD接口索引
# Fish TTS 配置
fish_apiKey: "" # Fish API密钥
fish_reference_id: "efc1ce3726a64bbc947d53a1465204aa" # 默认音色ID
enableTranslation: false # 是否开启翻译功能
targetLang: "JA" # 翻译目标语言 JA/EN
syncConfig: {} # 同传配置
fish_text_blacklist: []
# 直链配置
link_domain: "https://xiaozhian-slink.hf.space" # 直链服务器域名
# #ss 对话
ss_apiBaseUrl: ""
ss_Key: ""
ss_model: ""
ss_Prompt: ""
ss_useMarkdown: false # 是否使用markdown图片展示
ss_forwardMessage: true # 是否转发消息
ss_quoteMessage: true # 是否引用原消息
ss_forwardThinking: false # 是否转发思考过程
ss_isOnlyMaster: false # 默认配置是否仅限主人使用
ss_enableImageUpload: true # 是否启用图片上传功能
ss_APIList: [] # ss接口列表
ss_usingAPI: 0 # 当前主人使用的ss接口索引
# ss_userAPI: 0 # 当前用户使用的ss接口索引
# Gemini API配置
ggBaseUrl: ""
ggKey: ""
gg_useMarkdown: false # 是否使用markdown图片展示
gg_forwardMessage: true # 是否转发消息
gg_quoteMessage: true # 是否引用原消息
gg_Prompt: "" # 对话API提示词
gg_model: "" # 对话API模型
gg_ss_useContext: false # 是否启用上下文功能
gg_maxHistoryLength: 20 # 最大历史记录条数
gg_useSearch: true # 是否启用搜索功能
gg_enableImageGeneration: false # 是否启用文生图功能
gg_enableImageUpload: true # 是否启用图片上传功能
gg_HistoryExTime: 12
gg_isOnlyMaster: false # 默认配置是否仅限主人使用
gg_APIList: [] # gg接口列表
gg_usingAPI: 0 # 当前主人使用的gg接口索引
# gg_userAPI: 0 # 当前用户使用的gg接口索引
toggleAtMode: false
zhilOnlyMaster: false
groupMultiChat: false # 是否启用群聊多人对话功能
enablePrivateChatAI: true # 是否启用私聊模式的AI对话
douyinTV: true # 开启抖音解析
douyin_maxSizeMB: 50 # 抖音解析视频最大容量 MB
turnOnBilitv: true # 开启bili解析
bilitv_max_duration_min: 10 # bili解析视频最大分钟数
ggKey_free_250824: 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
ggReProxy: aHR0cHM6Ly9iLmdlbWluaXByb3h5LmdnZmYubmV0LGh0dHBzOi8vZC5nZW1pbmlwcm94eS5nZ2ZmLm5ldC9nZW1pbmksaHR0cHM6Ly9lLmdlbWluaXByb3h5LmdnZmYubmV0