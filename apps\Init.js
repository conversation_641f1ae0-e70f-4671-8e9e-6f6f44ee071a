import plugin from '../../../lib/plugins/plugin.js'
import Init from '../model/init.js'

export class init extends plugin {
    constructor() {
        super({
            /** 功能名称 */
            name: '视频解析插件-配置初始化',
            /** 功能描述 */
            dsc: '配置初始化',
            event: 'message',
            /** 优先级，数字越小等级越高 */
            priority: 1009,
            rule: [
                {
                    /** 命令正则匹配 */
                    reg: '^(/|#)配置初始化$',
                    /** 执行方法 */
                    fnc: 'init',
                    /** 主人判断 */
                    permission: "master",
                }
            ]
        })
    }

    async init(e) {
        const result = this.initConfig()
        if (result) {
            await e.reply('配置初始化完成', true)
        } else {
            await e.reply('配置初始化失败', true)
        }
        return true
    }
    
    initConfig() {
        try {
            const config_default_path = `${pluginRoot}/config/config_default.yaml`
            if (!fs.existsSync(config_default_path)) {
                logger.error('默认设置文件不存在，请检查或重新安装插件')
                return false
            }
            
            // 确保配置目录存在
            const configDir = `${pluginRoot}/config/config`
            if (!fs.existsSync(configDir)) {
                fs.mkdirSync(configDir, { recursive: true })
            }
            
            const config_path = `${pluginRoot}/config/config/config.yaml`
            if (!fs.existsSync(config_path)) {
                logger.info('设置文件不存在，将使用默认设置文件')
                fs.copyFileSync(config_default_path, config_path)
            }
            
            // 合并配置
            const config_default_yaml = Config.getDefConfig()
            const config_yaml = Config.getConfig()
            
            if (config_default_yaml && config_yaml) {
                // 添加缺失的配置项
                for (const key in config_default_yaml) {
                    if (!(key in config_yaml)) {
                        config_yaml[key] = config_default_yaml[key]
                    }
                }
                
                // 删除多余的配置项
                for (const key in config_yaml) {
                    if (!(key in config_default_yaml)) {
                        delete config_yaml[key]
                    }
                }
                
                Config.setConfig(config_yaml)
            }
            
            return true
        } catch (error) {
            logger.error('配置初始化失败:', error)
            return false
        }
    }
}
