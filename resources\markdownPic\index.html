<!DOCTYPE html>
<html lang="zh">

<head>
    <meta http-equiv="content-type" content="text/html;charset=utf-8" />
    <link rel="shortcut icon" href="#" />
    <link rel="stylesheet" type="text/css" href="{{_path}}/plugins/siliconflow-plugin/resources/common/common.css" />
    <link rel="preload" href="{{_path}}/plugins/siliconflow-plugin/resources/common/font/tttgbnumber.ttf" as="font" />
    <script src="{{_path}}/plugins/siliconflow-plugin/resources/markdownPic/marked.min.js"></script>
    <link rel="stylesheet" href="{{_path}}/plugins/siliconflow-plugin/resources/markdownPic/github.min.css">
    <script src="{{_path}}/plugins/siliconflow-plugin/resources/markdownPic/highlight.min.js"></script>
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    <script src="{{_path}}/plugins/siliconflow-plugin/resources/markdownPic/mathjax/tex-mml-chtml.js" async></script>
    <style>
        @font-face {
            font-family: "NZBZ";
            src: url('{{_path}}/plugins/siliconflow-plugin/resources/common/font/NZBZ.ttf') format('truetype');
        }

        @font-face {
            font-family: "HYWenHei";
            src: url('{{_path}}/plugins/siliconflow-plugin/resources/common/font/HYWH-65W.ttf') format('truetype');
        }

        @font-face {
            font-family: "HWSong";
            src: url('{{_path}}/plugins/siliconflow-plugin/resources/common/font/华文中宋.TTF') format('truetype');
        }

        @font-face {
            font-family: "SpaceMono";
            src: url('{{_path}}/plugins/siliconflow-plugin/resources/common/font/SpaceMono-Regular.ttf') format('truetype');
        }

        body {
            margin: 0;
            padding: 20px;
            padding-bottom: 45px;
            background-color: #ffe6f0;
            font-family: "HYWenHei", "HWSong", "Microsoft YaHei", sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
            font-weight: 500;
        }

        .chat-container {
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px 30px;
            padding-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 45px;
            word-wrap: break-word;
            overflow-wrap: break-word;
            min-height: fit-content;
            height: auto;
            overflow: visible;
            padding-bottom: 5px;
        }

        .message {
            display: flex;
            margin-bottom: 20px;
            align-items: flex-start;
            position: relative;
            padding-left: 60px;
            min-height: 51px;
        }

        .message.right {
            flex-direction: row-reverse;
            padding-left: 0;
            padding-right: 60px;
            margin-left: auto;
        }

        .avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            border: 3px solid #fff;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            position: absolute;
            top: 0;
        }

        .message.left .avatar {
            left: 0;
        }

        .message.right .avatar {
            right: 0;
        }

        .message-content {
            max-width: calc(100% - 10px);
            min-width: 50px;
            padding: 15px 20px;
            border-radius: 20px;
            position: relative;
            animation: pop-in 0.3s ease-out;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            width: fit-content;
            word-wrap: break-word;
            overflow-wrap: break-word;
            height: auto;
            overflow: visible;
            margin-top: 3px;
        }

        .left .message-content {
            background-color: #ffffff;
            border-top-left-radius: 5px;
            border-left: 4px solid #ffb6c1;
            box-shadow: 2px 2px 12px rgba(255, 182, 193, 0.2);
        }

        .left .message-content::before {
            content: '';
            position: absolute;
            left: -12px;
            top: 15px;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 8px 12px 8px 0;
            border-color: transparent #ffffff transparent transparent;
            filter: drop-shadow(-3px 0px 2px rgba(255, 182, 193, 0.1));
        }

        .right .message-content {
            background-color: #e3f2fd;
            border-top-right-radius: 5px;
            box-shadow: -2px 2px 12px rgba(227, 242, 253, 0.3);
        }

        .right .message-content::before {
            content: '';
            position: absolute;
            right: -12px;
            top: 15px;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 8px 0 8px 12px;
            border-color: transparent transparent transparent #e3f2fd;
        }

        @keyframes pop-in {
            0% {
                transform: scale(0.8);
                opacity: 0;
            }

            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .markdown-content {
            font-size: 18px;
            line-height: 1.8;
            color: #000000;
            font-weight: 500 !important;
            letter-spacing: 0.5px;
            word-wrap: break-word;
            overflow-wrap: break-word;
            max-width: 100%;
            height: auto;
            overflow: visible;
            font-family: "HYWenHei", "HWSong", "Microsoft YaHei", sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .markdown-content * {
            font-weight: inherit;
            color: inherit;
        }

        .markdown-content p {
            margin: 12px 0;
            line-height: 1.8;
            font-weight: 500;
            color: #000000;
        }

        .markdown-content code {
            font-family: "SpaceMono", "Consolas", monospace;
            background-color: #f8f8f8;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 0.95em;
            border: 1px solid #eee;
            color: #000000;
            font-weight: 500;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        .markdown-content pre {
            background-color: #f8f8f8;
            padding: 16px;
            border-radius: 8px;
            overflow: visible;
            border: 1px solid #eee;
            margin: 15px 0;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-width: 100%;
            color: #000000;
            font-weight: 500;
            font-family: "SpaceMono", "Consolas", monospace;
            line-height: 1.5;
        }

        .markdown-content pre code {
            font-weight: 500;
            color: #000000;
            padding: 0;
            background: none;
            border: none;
            display: block;
        }

        .markdown-content h1,
        .markdown-content h2,
        .markdown-content h3,
        .markdown-content h4,
        .markdown-content h5,
        .markdown-content h6 {
            font-family: "HYWenHei", sans-serif;
            color: #ff69b4;
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
            line-height: 1.25;
            letter-spacing: 0.5px;
        }

        .markdown-content h1 {
            font-size: 2.2em;
        }

        .markdown-content h2 {
            font-size: 1.7em;
        }

        .markdown-content h3 {
            font-size: 1.5em;
        }

        .markdown-content h4 {
            font-size: 1.3em;
        }

        .markdown-content h5 {
            font-size: 1.2em;
        }

        .markdown-content h6 {
            font-size: 1.1em;
        }

        .markdown-content a {
            color: #ff69b4;
            text-decoration: none;
            border-bottom: 1px solid #ff69b4;
            transition: all 0.3s ease;
        }

        .markdown-content a:hover {
            color: #ff1493;
            border-bottom-color: #ff1493;
        }

        .markdown-content img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 10px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .markdown-content table {
            width: 100%;
            max-width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            background: white;
            border-radius: 8px;
            overflow-x: auto;
            display: block;
        }

        .markdown-content th,
        .markdown-content td {
            padding: 12px;
            text-align: left;
            border: 1px solid #eee;
        }

        .markdown-content th {
            background: #ffb6c1;
            color: white;
            font-weight: bold;
        }

        .markdown-content tr:nth-child(even) {
            background: #fff5f7;
        }

        .markdown-content blockquote {
            margin: 10px 0;
            padding: 10px 20px;
            border-left: 4px solid #ffb6c1;
            background: #fff5f7;
            border-radius: 0 8px 8px 0;
        }

        .markdown-content ul,
        .markdown-content ol {
            padding-left: 20px;
        }

        .markdown-content li {
            margin: 5px 0;
        }

        .markdown-content hr {
            border: none;
            height: 2px;
            background: linear-gradient(to right, #ffb6c1, #ff69b4);
            margin: 20px 0;
        }

        .markdown-content input[type="checkbox"] {
            margin-right: 5px;
        }

        .markdown-content kbd {
            background-color: #f7f7f7;
            border: 1px solid #ccc;
            border-radius: 3px;
            box-shadow: 0 1px 0 rgba(0, 0, 0, 0.2);
            color: #333;
            display: inline-block;
            font-size: 0.9em;
            padding: 2px 6px;
            margin: 0 2px;
        }

        .cute-girl {
            position: absolute;
            right: 0px;
            bottom: 10px;
            width: 150px;
            height: auto;
            z-index: 1;
            pointer-events: none;
            opacity: 0.25;
            transition: opacity 0.3s ease;
        }

        .chat-container:hover .cute-girl {
            opacity: 0.4;
        }

        .powered-by {
            position: absolute;
            bottom: -35px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            font-family: "NZBZ", sans-serif;
            font-weight: 500;
            font-style: italic;
            letter-spacing: 1.5px;
            text-align: center;
            width: auto;
            padding: 4px 16px;
            z-index: 2;
            color: #fff;
            background: linear-gradient(to right, #ff69b4, #ffb6c1);
            border-radius: 20px;
            box-shadow: 0 2px 8px rgba(255, 105, 180, 0.15);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 添加滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f5f5f5;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: #ffb6c1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #ff69b4;
        }
    </style>
</head>

<body>
    <div class="chat-container">
        <div class="message right">
            <img class="avatar" src="https://q1.qlogo.cn/g?b=qq&s=0&nk={{userId}}" alt="用户头像">
            <div class="message-content">
                <div class="markdown-content">{{userMsg}}</div>
            </div>
        </div>

        <div class="message left">
            <img class="avatar" src="https://q1.qlogo.cn/g?b=qq&s=0&nk={{botId}}" alt="助手头像">
            <div class="message-content">
                <div class="markdown-content">{{content}}</div>
            </div>
        </div>

        <img class="cute-girl" src="{{_path}}/plugins/siliconflow-plugin/resources/readme/girl.png" alt="可爱的女孩">
        <div class="powered-by">Powered By SiliconFlow-Plugin</div>
    </div>

    <script>
        // 配置Marked选项
        marked.setOptions({
            renderer: new marked.Renderer(),
            highlight: function (code, lang) {
                const language = hljs.getLanguage(lang) ? lang : 'plaintext';
                return hljs.highlight(code, { language }).value;
            },
            langPrefix: 'hljs language-',
            pedantic: false,
            gfm: true,
            breaks: true,
            sanitize: false,
            smartypants: false,
            xhtml: false,
            escape: function(html) {
                return html.replace(/\\/g, '\\\\');
            }
        });

        // 初始化Markdown
        document.querySelectorAll('.markdown-content').forEach(element => {
            element.innerHTML = marked.parse(element.textContent);
            MathJax.typesetPromise && MathJax.typesetPromise();
        });

        // 确保内容完整显示
        function adjustContainerHeight() {
            const container = document.querySelector('.chat-container');
            const content = container.scrollHeight;
            container.style.minHeight = content + 'px';
        }

        // 在内容加载完成后调整高度
        window.addEventListener('load', adjustContainerHeight);
        // 在窗口大小改变时重新调整
        window.addEventListener('resize', adjustContainerHeight);
    </script>
</body>

</html>
