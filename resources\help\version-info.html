{{extend elemLayout}}

{{block 'css'}}
<link rel="stylesheet" type="text/css" href="{{_res_path}}/help/version-info.css"/>
{{/block}}

{{block 'main'}}
{{each changelogs ds idx}}
<div class="hydro-bg log-cont">
  {{set v = ds.version }}
  {{set isDev = v[v.length-1] === 'v'}}
  <div class="cont {{isDev ? 'dev-cont': ''}}">
    {{if idx === 0 }}
    <div class="cont-title current-version">当前版本 {{v}}</div>
    {{else}}
    <div class="cont-title">{{name || 'siliconflow-plugin'}}版本 {{v}}</div>
    {{/if}}
    <div class="cont-body">
      <ul class="log-ul">
        {{each ds.logs log}}
        <li>
          <p>{{@log.title}}</p>
          {{if log.logs.length > 0}}
          <ul class="sub-log-ul">
            {{each log.logs ls}}
            <li>{{@ls}}</li>
            {{/each}}
          </ul>
          {{/if}}
        </li>
        {{/each}}
      </ul>
    </div>
  </div>
</div>
{{/each}}
{{/block}}